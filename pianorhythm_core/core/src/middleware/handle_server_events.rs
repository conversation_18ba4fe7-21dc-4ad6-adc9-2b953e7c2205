use protobuf::Message;
use reactive_state::middleware::{Middleware, NotifyFn, ReduceFn, ReduceMiddlewareResult};

use pianorhythm_proto::client_message::{ClientMessage, ClientMessageType, ClientValidationErrorMsgType, CommandResponseType};
use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_proto::user_renditions::Roles;

use crate::middleware::handle_client_actions::client_message_to_action;
use crate::reducers::app_state::AppState;

pub struct HandleServerEventsMiddleware<'c> {
    pub core_api: &'c crate::types::CoreClientApiType,
}

impl<'c> Middleware<AppState, AppStateActions, AppStateEvents, AppStateEffects> for HandleServerEventsMiddleware<'c> {
    fn on_reduce(
        &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, action: Option<&AppStateActions>,
        reduce: ReduceFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
        if let Some(action) = action {
            let mut effect = AppStateEffects::new();

            // log::info!("Handling server event: {:?}", action.get_action());

            match action.action {
                AppStateActions_Action::EmitToast if action.has_stringValue() || action.has_appNotificationConfig() => {
                    effect.set_action(AppStateEffects_Action::Toast);

                    if action.has_stringValue() {
                        effect.set_message(action.get_stringValue().to_string());
                    } else {
                        effect.set_appNotificationConfig(action.get_appNotificationConfig().clone());
                    }
                }
                AppStateActions_Action::FailToJoinRoom if action.has_joinRoomFailResponse() => {
                    effect.set_action(AppStateEffects_Action::JoinRoomFailResponse);
                    effect.set_joinRoomFailResponse(action.get_joinRoomFailResponse().clone());
                }
                AppStateActions_Action::GetRoomFullDetails if action.has_roomFullDetails() => {
                    effect.set_action(AppStateEffects_Action::SetRoomFullDetails);
                    effect.set_roomFullDetails(action.get_roomFullDetails().clone());
                }
                AppStateActions_Action::ServerToClientMessage if action.has_commandResponse() => {
                    let command_response = action.get_commandResponse().clone();

                    match command_response.messageType {
                        CommandResponseType::ValidationError if command_response.has_validationErrorList() => {
                            let validation_error_list = command_response.get_validationErrorList();

                            match validation_error_list.errorType {
                                ClientValidationErrorMsgType::CreateRoom => {
                                    effect.set_action(AppStateEffects_Action::CreateRoomValidationErrors);
                                    effect.set_clientValidationErrorList(validation_error_list.clone());
                                }
                                ClientValidationErrorMsgType::UpdateRoom => {
                                    effect.set_action(AppStateEffects_Action::UpdateRoomValidationErrors);
                                    effect.set_clientValidationErrorList(validation_error_list.clone());
                                }
                                _ => {}
                            }
                        }
                        _ => {}
                    }
                }
                _ => {}
            }

            if effect.is_initialized() && effect.action != AppStateEffects_Action::Unknown {
                self.core_api.dispatch_app_effect(&effect);
            }
        }
        reduce(store, action)
    }

    fn process_effect(
        &self, _store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, effect: AppStateEffects,
    ) -> Option<AppStateEffects> {
        Some(effect)
    }

    fn on_notify(
        &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, events: Vec<AppStateEvents>,
        notify: NotifyFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
    ) -> Vec<AppStateEvents> {
        let events = notify(store, events);

        if events.contains(&AppStateEvents::RoomChatHistorySet) {
            let room_settings = store.state().current_room_state.room_settings.clone();

            if room_settings.has_WelcomeMessage() {
                if let Some(client) = store.state().client_state.client.clone() {
                    let mut client_message = ClientMessage::new();
                    client_message.set_messageType(ClientMessageType::LoadRoomWelcomeMessage);
                    let mut message = room_settings.get_WelcomeMessage().to_string();

                    let mut usertag = format!("<user uuid={} />", client.get_userDto().get_usertag());
                    if client.get_userDto().get_roles().contains(&Roles::GUEST) {
                        // Setting the usertag to the raw value for guests because the
                        // <user> syntax doesn't show anything for guests...
                        usertag = client.get_userDto().get_usertag().to_string();
                    }
                    message = message.replace("%%%%user%%%%", &usertag).replace("%%user%%", &usertag).trim().to_string();

                    if room_settings.RoomOwner == client.get_userDto().socketID {
                        let target_text = "welcome to my room";
                        if message.to_lowercase().contains(target_text) {
                            message = message.replace(target_text, &target_text.replace("my", "your"))
                        }
                    }

                    client_message.set_stringValue(message);
                    store.dispatch(client_message_to_action(&client_message));
                }
            }
        }

        events
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::reducers::app_state::{AppState};
    use pianorhythm_proto::client_message::{
        ChatMessageDto, ClientMessage, CommandResponse,
        CommandResponse_ClientValidationErrorList as ClientValidationErrorList,
        CommandResponse_JoinRoomFailResponse as JoinRoomFailResponse, CommandResponseType,
        JoinRoomFailType, ClientMessageType, ClientValidationErrorMsgType,
    };
    use pianorhythm_proto::pianorhythm_app_renditions::AppNotificationConfig;
    use pianorhythm_proto::room_renditions::{RoomFullDetails, RoomSettings};
    use pianorhythm_proto::user_renditions::{Roles, UserClientDto, UserDto};
    use reactive_state::middleware::{ReduceFn, Middleware, ReduceMiddlewareResult};
    use reactive_state::{Reducer, ReducerResult, Store};
    use std::cell::RefCell;
    use std::rc::Rc;
    use std::sync::{Arc, Mutex};

    // --- Mocks and Test Helpers ---

    /// A mock CoreClientApi to track dispatched effects.
    #[derive(Clone, Default)]
    struct MockCoreClientApi {
        dispatched_effects: Arc<Mutex<Vec<AppStateEffects>>>,
    }

    /// Test version of HandleServerEventsMiddleware that owns the API instead of borrowing it
    /// This is a simple wrapper around HandleServerEventsMiddleware for testing purposes
    struct TestHandleServerEventsMiddleware {
        core_api: Box<dyn crate::types::CoreClientApi + Send + Sync>,
    }

    impl TestHandleServerEventsMiddleware {
        fn new(core_api: Box<dyn crate::types::CoreClientApi + Send + Sync>) -> Self {
            Self { core_api }
        }
    }

    impl Middleware<AppState, AppStateActions, AppStateEvents, AppStateEffects> for TestHandleServerEventsMiddleware {
        fn on_reduce(
            &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, action: Option<&AppStateActions>,
            reduce: ReduceFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
        ) -> ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
            // Delegate to the real implementation by creating a temporary HandleServerEventsMiddleware
            let real_middleware = HandleServerEventsMiddleware { core_api: &self.core_api };
            real_middleware.on_reduce(store, action, reduce)
        }

        fn process_effect(
            &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, effect: AppStateEffects,
        ) -> Option<AppStateEffects> {
            // Delegate to the real implementation
            let real_middleware = HandleServerEventsMiddleware { core_api: &self.core_api };
            real_middleware.process_effect(store, effect)
        }

        fn on_notify(
            &self, store: &crate::Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>, events: Vec<AppStateEvents>,
            notify: NotifyFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
        ) -> Vec<AppStateEvents> {
            // Delegate to the real implementation
            let real_middleware = HandleServerEventsMiddleware { core_api: &self.core_api };
            real_middleware.on_notify(store, events, notify)
        }
    }

    // This implementation now allows the mock to be part of a thread-safe trait object.
    impl crate::types::CoreClientApi for MockCoreClientApi {
        fn init(&mut self) -> () {
            // Mock implementation - do nothing
        }

        fn ws_emit_binary(&self, _bytes: Vec<u8>) -> () {
            // Mock implementation - do nothing
        }

        fn dispatch_app_effect(&self, effect: &AppStateEffects) {
            self.dispatched_effects.lock().unwrap().push(effect.clone());
        }

        #[cfg(feature = "use_synth")]
        fn dispatch_midi_synth_event(&self, _event: &pianorhythm_synth::PianoRhythmSynthEvent) -> () {
            // Mock implementation - do nothing
        }
    }



    /// A test middleware to log actions dispatched to the store.
    #[derive(Clone, Default)]
    struct ActionLoggerMiddleware {
        logged_actions: Arc<Mutex<RefCell<Vec<AppStateActions>>>>,
    }

    impl Middleware<AppState, AppStateActions, AppStateEvents, AppStateEffects> for ActionLoggerMiddleware {
        fn on_reduce(
            &self,
            store: &Store<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
            action: Option<&AppStateActions>,
            reduce: ReduceFn<AppState, AppStateActions, AppStateEvents, AppStateEffects>,
        ) -> ReduceMiddlewareResult<AppStateEvents, AppStateEffects> {
            if let Some(action) = action {
                self.logged_actions
                    .lock()
                    .unwrap()
                    .borrow_mut()
                    .push(action.clone());
            }
            reduce(store, action)
        }
    }

    /// A reducer that can be configured to produce specific events for testing.
    #[derive(Default)]
    struct TestReducerWithEvents {
        events_to_produce: Vec<AppStateEvents>,
    }

    impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for TestReducerWithEvents {
        fn reduce(
            &self,
            state: &Rc<AppState>,
            _action: &AppStateActions,
        ) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
            ReducerResult {
                state: state.clone(),
                events: self.events_to_produce.clone(),
                effects: vec![],
            }
        }
    }

    /// A simple reducer for test purposes. It does not modify state or produce events/effects.
    struct TestReducer;
    impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for TestReducer {
        fn reduce(
            &self,
            state: &Rc<AppState>,
            _action: &AppStateActions,
        ) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
            ReducerResult {
                state: state.clone(),
                events: vec![],
                effects: vec![],
            }
        }
    }

    /// Helper to create a test store with a default state and a simple reducer.
    fn create_test_store(state: AppState) -> Store<AppState, AppStateActions, AppStateEvents, AppStateEffects> {
        Store::new(TestReducer, state)
    }

    /// Dummy reduce function for middleware calls.
    fn dummy_reduce_fn<S, A, E, F>(
        _store: &Store<S, A, E, F>,
        _action: Option<&A>,
    ) -> ReduceMiddlewareResult<E, F> {
        ReduceMiddlewareResult {
            events: vec![],
            effects: vec![],
        }
    }

    // --- on_reduce Method Tests ---

    #[test]
    fn on_reduce_should_dispatch_toast_effect_for_string_value() {
        // Arrange
        let mock_api = MockCoreClientApi::default();
        let dispatched_effects_arc = mock_api.dispatched_effects.clone();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let middleware = HandleServerEventsMiddleware { core_api: &boxed_api };
        let store = create_test_store(AppState::new());

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::EmitToast);
        action.set_stringValue("Hello, World!".to_string());

        // Act
        middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        let effects = dispatched_effects_arc.lock().unwrap().clone();
        assert_eq!(effects.len(), 1);
        let effect = &effects[0];
        assert_eq!(effect.get_action(), AppStateEffects_Action::Toast);
        assert_eq!(effect.get_message(), "Hello, World!");
    }

    #[test]
    fn on_reduce_should_dispatch_toast_effect_for_notification_config() {
        // Arrange
        let mock_api = MockCoreClientApi::default();
        let dispatched_effects_arc = mock_api.dispatched_effects.clone();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let middleware = HandleServerEventsMiddleware { core_api: &boxed_api };
        let store = create_test_store(AppState::new());

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::EmitToast);
        let mut config = AppNotificationConfig::new();
        config.set_title("Configured Toast".to_string());
        action.set_appNotificationConfig(config.clone());

        // Act
        middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        let effects = dispatched_effects_arc.lock().unwrap().clone();
        assert_eq!(effects.len(), 1);
        let effect = &effects[0];
        assert_eq!(effect.get_action(), AppStateEffects_Action::Toast);
        assert_eq!(effect.get_appNotificationConfig(), &config);
    }

    #[test]
    fn on_reduce_should_dispatch_fail_to_join_room_effect() {
        // Arrange
        let mock_api = MockCoreClientApi::default();
        let dispatched_effects_arc = mock_api.dispatched_effects.clone();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let middleware = HandleServerEventsMiddleware { core_api: &boxed_api };
        let store = create_test_store(AppState::new());

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::FailToJoinRoom);
        let mut fail_response = JoinRoomFailResponse::new();
        fail_response.set_reason(JoinRoomFailType::RoomFull);
        action.set_joinRoomFailResponse(fail_response.clone());

        // Act
        middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        let effects = dispatched_effects_arc.lock().unwrap().clone();
        assert_eq!(effects.len(), 1);
        let effect = &effects[0];
        assert_eq!(effect.get_action(), AppStateEffects_Action::JoinRoomFailResponse);
        assert_eq!(effect.get_joinRoomFailResponse(), &fail_response);
    }

    #[test]
    fn on_reduce_should_dispatch_set_room_full_details_effect() {
        // Arrange
        let mock_api = MockCoreClientApi::default();
        let dispatched_effects_arc = mock_api.dispatched_effects.clone();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let middleware = HandleServerEventsMiddleware { core_api: &boxed_api };
        let store = create_test_store(AppState::new());

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::GetRoomFullDetails);
        let mut details = RoomFullDetails::new();
        details.set_roomName("Test Room".to_string());
        action.set_roomFullDetails(details.clone());

        // Act
        middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        let effects = dispatched_effects_arc.lock().unwrap().clone();
        assert_eq!(effects.len(), 1);
        let effect = &effects[0];
        assert_eq!(
            effect.get_action(),
            AppStateEffects_Action::SetRoomFullDetails
        );
        assert_eq!(effect.get_roomFullDetails(), &details);
    }

    #[test]
    fn on_reduce_should_dispatch_create_room_validation_errors() {
        // Arrange
        let mock_api = MockCoreClientApi::default();
        let dispatched_effects_arc = mock_api.dispatched_effects.clone();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let middleware = HandleServerEventsMiddleware { core_api: &boxed_api };
        let store = create_test_store(AppState::new());

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::ServerToClientMessage);
        let mut command_response = CommandResponse::new();
        command_response.set_messageType(CommandResponseType::ValidationError);
        let mut error_list = ClientValidationErrorList::new();
        error_list.set_errorType(ClientValidationErrorMsgType::CreateRoom);
        error_list.set_data(protobuf::RepeatedField::from_vec(vec![]));
        command_response.set_validationErrorList(error_list.clone());
        action.set_commandResponse(command_response);

        // Act
        middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        let effects = dispatched_effects_arc.lock().unwrap().clone();
        assert_eq!(effects.len(), 1);
        let effect = &effects[0];
        assert_eq!(
            effect.get_action(),
            AppStateEffects_Action::CreateRoomValidationErrors
        );
        assert_eq!(effect.get_clientValidationErrorList(), &error_list);
    }

    #[test]
    fn on_reduce_should_dispatch_update_room_validation_errors() {
        // Arrange
        let mock_api = MockCoreClientApi::default();
        let dispatched_effects_arc = mock_api.dispatched_effects.clone();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let middleware = HandleServerEventsMiddleware { core_api: &boxed_api };
        let store = create_test_store(AppState::new());

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::ServerToClientMessage);
        let mut command_response = CommandResponse::new();
        command_response.set_messageType(CommandResponseType::ValidationError);
        let mut error_list = ClientValidationErrorList::new();
        error_list.set_errorType(ClientValidationErrorMsgType::UpdateRoom);
        error_list.set_data(protobuf::RepeatedField::from_vec(vec![]));
        command_response.set_validationErrorList(error_list.clone());
        action.set_commandResponse(command_response);

        // Act
        middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        let effects = dispatched_effects_arc.lock().unwrap().clone();
        assert_eq!(effects.len(), 1);
        let effect = &effects[0];
        assert_eq!(
            effect.get_action(),
            AppStateEffects_Action::UpdateRoomValidationErrors
        );
        assert_eq!(effect.get_clientValidationErrorList(), &error_list);
    }

    #[test]
    fn on_reduce_should_not_dispatch_effect_for_unhandled_actions() {
        // Arrange
        let mock_api = MockCoreClientApi::default();
        let dispatched_effects_arc = mock_api.dispatched_effects.clone();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let middleware = HandleServerEventsMiddleware { core_api: &boxed_api };
        let store = create_test_store(AppState::new());

        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::UpdateClient); // An unhandled action

        // Act
        middleware.on_reduce(&store, Some(&action), dummy_reduce_fn);

        // Assert
        let effects = dispatched_effects_arc.lock().unwrap().clone();
        assert!(effects.is_empty());
    }

    #[test]
    fn on_reduce_should_not_dispatch_effect_when_action_is_none() {
        // Arrange
        let mock_api = MockCoreClientApi::default();
        let dispatched_effects_arc = mock_api.dispatched_effects.clone();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let middleware = HandleServerEventsMiddleware { core_api: &boxed_api };
        let store = create_test_store(AppState::new());

        // Act
        middleware.on_reduce(&store, None, dummy_reduce_fn);

        // Assert
        let effects = dispatched_effects_arc.lock().unwrap().clone();
        assert!(effects.is_empty());
    }

    // --- on_notify Method Tests ---

    fn setup_state_for_notify(
        welcome_message: Option<String>,
        usertag: &str,
        is_guest: bool,
        is_owner: bool,
    ) -> AppState {
        let mut state = AppState::new();

        let mut room_settings = RoomSettings::new();
        if let Some(msg) = welcome_message {
            room_settings.set_WelcomeMessage(msg);
        }

        let mut user_dto = UserDto::new();
        user_dto.set_usertag(usertag.to_string());
        user_dto.set_socketID(12345.to_string());
        if is_guest {
            user_dto.set_roles(vec![Roles::GUEST]);
        }

        if is_owner {
            room_settings.set_RoomOwner(user_dto.get_socketID().to_string());
        } else {
            room_settings.set_RoomOwner("99999".to_string()); // Different owner
        }

        let mut client = UserClientDto::new();
        client.set_userDto(user_dto);

        state.client_state = crate::reducers::client_state::ClientState {
            client: Some(client),
            ..Default::default()
        };
        state.current_room_state = crate::reducers::current_room_state::CurrentRoomState {
            room_settings: room_settings,
            ..Default::default()
        };

        state
    }

    #[ignore]
    #[test]
    fn on_notify_should_dispatch_welcome_message_for_non_guest() {
        // Arrange
        let action_logger = ActionLoggerMiddleware::default();
        let logged_actions_arc = action_logger.logged_actions.clone();
        let mock_api = MockCoreClientApi::default();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let handle_events_middleware = TestHandleServerEventsMiddleware::new(boxed_api);
        let state = setup_state_for_notify(
            Some("Welcome %%user%%!".to_string()),
            "test-user-123",
            false,
            false,
        );
        let reducer = TestReducerWithEvents { events_to_produce: vec![AppStateEvents::RoomChatHistorySet] };
        let store = Store::new(reducer, state);
        store.add_middleware(action_logger);
        store.add_middleware(handle_events_middleware);

        // Act
        store.dispatch(AppStateActions::new()); // Dummy action to trigger the cycle

        // Assert
        let logged_actions = logged_actions_arc.lock().unwrap().borrow().clone();
        assert_eq!(logged_actions.len(), 2);
        let dispatched_action = &logged_actions[1];
        assert_eq!(dispatched_action.get_action(), AppStateActions_Action::AddRoomChatMessage);
        let client_msg = dispatched_action.get_clientMsg();
        assert_eq!(client_msg.get_messageType(), ClientMessageType::LoadRoomWelcomeMessage);
        assert_eq!(client_msg.get_stringValue(), "Welcome <user uuid=test-user-123 />!");
    }

    #[ignore]
    #[test]
    fn on_notify_should_dispatch_welcome_message_for_guest() {
        // Arrange
        let action_logger = ActionLoggerMiddleware::default();
        let logged_actions_arc = action_logger.logged_actions.clone();
        let mock_api = MockCoreClientApi::default();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let handle_events_middleware = TestHandleServerEventsMiddleware::new(boxed_api);
        let state = setup_state_for_notify(
            Some("Hello %%%%user%%%%, welcome.".to_string()),
            "guest-abc",
            true,
            false,
        );
        let store = Store::new(TestReducerWithEvents { events_to_produce: vec![AppStateEvents::RoomChatHistorySet] }, state);
        store.add_middleware(action_logger);
        store.add_middleware(handle_events_middleware);

        // Act
        store.dispatch(AppStateActions::new());

        // Assert
        let logged_actions = logged_actions_arc.lock().unwrap().borrow().clone();
        assert_eq!(logged_actions.len(), 2);
        let dispatched_action = &logged_actions[1];
        assert_eq!(dispatched_action.get_action(), AppStateActions_Action::AddRoomChatMessage);
        let client_msg = dispatched_action.get_clientMsg();
        assert_eq!(client_msg.get_stringValue(), "Hello guest-abc, welcome.");
    }

    #[ignore]
    #[test]
    fn on_notify_should_modify_my_room_to_your_room_for_owner() {
        // Arrange
        let action_logger = ActionLoggerMiddleware::default();
        let logged_actions_arc = action_logger.logged_actions.clone();
        let mock_api = MockCoreClientApi::default();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let handle_events_middleware = TestHandleServerEventsMiddleware::new(boxed_api);
        let state = setup_state_for_notify(
            Some("%%user%%, welcome to my room!".to_string()),
            "owner-user",
            false,
            true,
        );
        let store = Store::new(TestReducerWithEvents { events_to_produce: vec![AppStateEvents::RoomChatHistorySet] }, state);
        store.add_middleware(action_logger);
        store.add_middleware(handle_events_middleware);

        // Act
        store.dispatch(AppStateActions::new());

        // Assert
        let logged_actions = logged_actions_arc.lock().unwrap().borrow().clone();
        assert_eq!(logged_actions.len(), 2);
        let dispatched_action = &logged_actions[1];
        assert_eq!(dispatched_action.get_action(), AppStateActions_Action::AddRoomChatMessage);
        let client_msg = dispatched_action.get_clientMsg();
        assert_eq!(client_msg.get_stringValue(), "<user uuid=owner-user />, welcome to your room!");
    }

    #[test]
    fn on_notify_should_not_dispatch_if_no_welcome_message() {
        // Arrange
        let action_logger = ActionLoggerMiddleware::default();
        let logged_actions_arc = action_logger.logged_actions.clone();
        let mock_api = MockCoreClientApi::default();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let handle_events_middleware = TestHandleServerEventsMiddleware::new(boxed_api);
        let state = setup_state_for_notify(None, "any-user", false, false);
        let store = Store::new(TestReducerWithEvents { events_to_produce: vec![AppStateEvents::RoomChatHistorySet] }, state);
        store.add_middleware(action_logger);
        store.add_middleware(handle_events_middleware);

        // Act
        store.dispatch(AppStateActions::new());

        // Assert
        let logged_actions = logged_actions_arc.lock().unwrap().borrow().clone();
        assert_eq!(logged_actions.len(), 1);
    }

    #[test]
    fn on_notify_should_not_dispatch_if_event_is_not_room_chat_history_set() {
        // Arrange
        let action_logger = ActionLoggerMiddleware::default();
        let logged_actions_arc = action_logger.logged_actions.clone();
        let mock_api = MockCoreClientApi::default();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let handle_events_middleware = TestHandleServerEventsMiddleware::new(boxed_api);
        let state = setup_state_for_notify(
            Some("Welcome!".to_string()),
            "any-user",
            false,
            false,
        );
        let store = Store::new(TestReducerWithEvents { events_to_produce: vec![AppStateEvents::AppStateReset] }, state);
        store.add_middleware(action_logger);
        store.add_middleware(handle_events_middleware);

        // Act
        store.dispatch(AppStateActions::new());

        // Assert
        let logged_actions = logged_actions_arc.lock().unwrap().borrow().clone();
        assert_eq!(logged_actions.len(), 1);
    }

    #[test]
    fn on_notify_should_not_dispatch_if_client_is_not_set() {
        // Arrange
        let action_logger = ActionLoggerMiddleware::default();
        let logged_actions_arc = action_logger.logged_actions.clone();
        let mock_api = MockCoreClientApi::default();
        let boxed_api: Box<dyn crate::types::CoreClientApi + Send + Sync> = Box::new(mock_api);
        let handle_events_middleware = TestHandleServerEventsMiddleware::new(boxed_api);
        let mut state = setup_state_for_notify(
            Some("Welcome!".to_string()),
            "any-user",
            false,
            false,
        );
        state.client_state.client = None; // No client in state
        let store = Store::new(TestReducerWithEvents { events_to_produce: vec![AppStateEvents::RoomChatHistorySet] }, state);
        store.add_middleware(action_logger);
        store.add_middleware(handle_events_middleware);

        // Act
        store.dispatch(AppStateActions::new());

        // Assert
        let logged_actions = logged_actions_arc.lock().unwrap().borrow().clone();
        assert_eq!(logged_actions.len(), 1);
    }
}


