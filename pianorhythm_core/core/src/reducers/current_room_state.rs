/// This module contains the definition of the `CurrentRoomState` struct and its associated reducer.
///
/// The `CurrentRoomState` struct represents the state of the current room in the application. It contains information such as the room ID, room owner, room stage details, users typing, room settings, users in the room, chat messages, muted notes users, muted chat users, and room stage loading status.
///
/// The `CurrentRoomStateReducer` struct is the reducer responsible for handling actions related to the current room state. It implements the `Reducer` trait and defines the `reduce` method, which takes the previous state and an action as input and returns a `ReducerResult` containing the updated state, events, and effects.
///
/// The `update_user_from_command` function is a helper function used by the reducer to update a user's information based on a user update command. It takes a `UserDto` and a `UserUpdateCommand` as input and returns the updated `UserDto`.
///
/// The module also includes unit tests for the `update_user_from_command` function.
use std::fmt::Debug;
use std::ops::Deref;
use std::rc::Rc;

use protobuf::{Clear, Message, RepeatedField};
use reactive_state::{Reducer, ReducerResult};
use rustc_hash::{FxHashMap, FxHashSet};
use serde::Serialize;

use pianorhythm_proto::client_message::{ChatMessageDto, ChatMessageDtoList, ClientSideUserDtoList};
use pianorhythm_proto::pianorhythm_actions::{AppStateActions, AppStateActions_Action};
use pianorhythm_proto::pianorhythm_app_renditions::AppPageloaderDetail;
use pianorhythm_proto::pianorhythm_effects::{AppStateEffects, AppStateEffects_Action, AppStateEffects_LoadRoomStageDetails};
use pianorhythm_proto::pianorhythm_events::AppStateEvents;
use pianorhythm_proto::room_renditions::{RoomSettings, RoomStageDetails, RoomStages};
use pianorhythm_proto::user_renditions::{ClientSideUserDto, UserDto, UserUpdateCommand};

use crate::reducers::app_state::AppState;
use crate::utils::{RoomId, SocketId};

#[derive(Clone, Default, Debug, PartialEq, Serialize)]
pub struct CurrentRoomState {
    /// The ID of the room, if it exists.
    pub room_id: Option<RoomId>,
    /// The owner of the room, if any.
    pub room_owner: Option<String>,
    #[serde(serialize_with = "pianorhythm_shared::util::option_proto_serialize")]
    pub room_stage_details: Option<RoomStageDetails>,
    /// A vector containing the socket IDs of users who are currently typing in the room.
    pub users_typing: Vec<SocketId>,
    #[serde(serialize_with = "pianorhythm_shared::util::proto_serialize")]
    /// The settings for the room.
    pub room_settings: RoomSettings,
    #[serde(serialize_with = "pianorhythm_shared::util::hashmap_proto_serialize")]
    /// A map that stores the users in the current room.
    pub users: FxHashMap<SocketId, ClientSideUserDto>,
    #[serde(serialize_with = "pianorhythm_shared::util::vec_proto_serialize")]
    /// Represents a collection of chat messages in the current room state.
    pub chat_messages: Vec<ChatMessageDto>,
    /// List of socket IDs representing users who are muted in the chat.
    pub muted_chat_users: FxHashSet<String>,
    /// Indicates whether the room stage is currently loading.
    pub room_stage_loading: bool,
    /// Indicates whether the room stage has been loaded.
    pub room_stage_loaded: bool,
    pub in_self_hosted_room: bool,
}

#[derive(Clone, Default)]
pub struct CurrentRoomStateReducer;

fn generate_users_list_effect(state: &CurrentRoomState) -> AppStateEffects {
    let mut effect = AppStateEffects::new();
    effect.set_action(AppStateEffects_Action::UsersSet);

    let mut list = ClientSideUserDtoList::new();
    let users = state.users.clone().into_values().collect::<Vec<_>>();
    list.set_list(RepeatedField::from_vec(users));
    effect.set_clientSideUserDtoList(list);
    effect
}

impl Reducer<AppState, AppStateActions, AppStateEvents, AppStateEffects> for CurrentRoomStateReducer {
    fn reduce(&self, prev_state: &Rc<AppState>, action: &AppStateActions) -> ReducerResult<AppState, AppStateEvents, AppStateEffects> {
        let mut events: Vec<AppStateEvents> = Vec::new();
        let mut effects: Vec<AppStateEffects> = Vec::new();
        let mut new_room_state = prev_state.deref().clone().current_room_state;
        let mut new_room_state_users = new_room_state.clone().users;

        let user_dto_to_client_side = |x: &UserDto| {
            let mut target = ClientSideUserDto::new();
            target.set_socketID(x.get_socketID().to_string());
            target.set_userDto(x.clone());
            target.set_socketIDHashed(crate::utils::hash_socket_id(x.get_socketID()).unwrap_or_default());
            target
        };

        let create_set_chat_messages_effect = |chat_messages: &Vec<ChatMessageDto>| {
            let mut effect = AppStateEffects::new();
            effect.set_action(AppStateEffects_Action::SetChatMessages);
            let mut list = ChatMessageDtoList::new();
            list.set_messages(RepeatedField::from_vec(chat_messages.clone()));
            effect.set_chatMessageDtoList(list);
            effect
        };

        let update_list_with_client = |users: &mut FxHashMap<SocketId, ClientSideUserDto>| {
            if let Some(user_client) = prev_state.deref().client_state.client.clone() {
                let socket_id = SocketId::from(user_client.get_userDto().get_socketID());
                let target = user_dto_to_client_side(&user_client.get_userDto());
                let added = users.insert(socket_id, target.clone());
                if added != Some(target) {
                    return Some(user_client);
                }
                return None;
            }

            None
        };

        match action.action {
            AppStateActions_Action::JoinedRoom if action.has_joinedRoomData() => 'joined_room: {
                let room_data = action.get_joinedRoomData().clone();
                let room_id = RoomId::from(room_data.get_roomID());

                if prev_state.current_room_state.room_id == Some(room_id.clone()) {
                    break 'joined_room;
                }

                if room_data.has_roomSettings() {
                    new_room_state.room_settings = room_data.roomSettings.clone().unwrap_or_default();
                    let stage_details = room_data.get_roomSettings().get_StageDetails();

                    if stage_details.is_initialized() {
                        new_room_state.room_stage_details = Some(stage_details.clone());
                    } else {
                        new_room_state.room_stage_details = None;

                        #[cfg(debug_assertions)]
                        log::warn!("stage details not initialized.");
                    }
                } else {
                    new_room_state.room_settings.clear();
                    new_room_state.room_stage_details = None;
                }

                new_room_state.room_owner = Some(room_data.get_roomOwner().to_string());
                new_room_state.room_id = Some(room_id);
                new_room_state_users.clear();
                new_room_state.users = new_room_state_users;
                new_room_state.room_stage_loading = true;
                new_room_state.in_self_hosted_room = !room_data.get_selfHostedCountryCode().is_empty();

                #[cfg(debug_assertions)]
                log::info!("SelfHostCode: {:?} | Prev: {}", room_data.get_selfHostedCountryCode(), prev_state.current_room_state.in_self_hosted_room);

                let room_stage = new_room_state.room_stage_details.clone().map(|x| x.stage.clone()).unwrap_or(RoomStages::THE_VOID);

                effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::JoinedRoomSuccess, |effect| {
                    effect.set_joinedRoomData(room_data.clone());
                }));

                effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::SetPageLoaderDetails, |effect| {
                    let mut detail = AppPageloaderDetail::new();
                    detail.set_active(true);
                    detail.set_details(format!("Loading Stage: <b>{:?}</b>", &room_stage));
                    effect.set_appPageLoaderDetail(detail);
                }));

                effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::LoadRoomStage, |effect| {
                    let mut details = AppStateEffects_LoadRoomStageDetails::new();
                    details.set_roomStage(room_stage);
                    details.set_roomType(new_room_state.room_settings.RoomType);
                    details.set_roomStageDetails(new_room_state.room_stage_details.clone().unwrap_or_default());
                    effect.set_loadRoomStageDetails(details);
                }));

                if new_room_state.in_self_hosted_room {
                    events.push(AppStateEvents::JoinedSelfHostedRoom);
                } else if prev_state.current_room_state.in_self_hosted_room {
                    events.push(AppStateEvents::LeftSelfHostedRoom);
                }

                events.push(AppStateEvents::RoomStageLoading);

                #[cfg(debug_assertions)]
                log::debug!("Joined room: {:?} | Now trigger stage loading.", &new_room_state.room_id);
            }
            AppStateActions_Action::SetRoomStageLoaded => {
                let bool_value = action.get_boolValue();
                new_room_state.room_stage_loaded = bool_value;
                if bool_value {
                    new_room_state.room_stage_loading = false;
                    events.push(AppStateEvents::RoomStageLoaded);
                }
            }
            AppStateActions_Action::AddUser if action.has_userDto() => {
                let target = action.get_userDto();

                let added = new_room_state_users.insert(SocketId::from(target.socketID.clone()), user_dto_to_client_side(&target));
                update_list_with_client(&mut new_room_state_users);
                new_room_state.users = new_room_state_users;

                if added.is_none() {
                    effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::AddUser, |effect| {
                        effect.set_clientSideUserDto(user_dto_to_client_side(&target));
                    }));
                }
            }
            AppStateActions_Action::UpdateUser if action.has_userUpdateCommand() => {
                let update_command = action.get_userUpdateCommand();
                let socket_id = SocketId::from(update_command.socketID.clone());

                if let Some(user_dto) = new_room_state_users.get(&socket_id) {
                    let target = update_user_from_command(user_dto.get_userDto(), update_command);

                    if target != user_dto.get_userDto().clone() {
                        _ = new_room_state_users.insert(socket_id, user_dto_to_client_side(&target));
                        update_list_with_client(&mut new_room_state_users);
                        new_room_state.users = new_room_state_users;

                        effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::UpdateUser, |effect| {
                            effect.set_clientSideUserDto(user_dto_to_client_side(&target));
                        }));
                    }
                }
            }
            AppStateActions_Action::RemoveUser if action.has_socketId() => {
                let removed = new_room_state_users.remove(&SocketId::from(action.get_socketId().to_string()));
                new_room_state.users = new_room_state_users;

                if removed.is_some() {
                    effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::RemoveUser, |effect| {
                        effect.set_socketId(action.get_socketId().to_string());
                    }));
                }
            }
            AppStateActions_Action::SetUsers if action.has_userDtoList() => {
                action.get_userDtoList().userDto.clone().iter().for_each(|x| {
                    new_room_state_users.insert(SocketId::from(x.socketID.clone()), user_dto_to_client_side(&x));
                });
                update_list_with_client(&mut new_room_state_users);
                new_room_state.users = new_room_state_users;

                effects.push(generate_users_list_effect(&new_room_state));
                events.push(AppStateEvents::UsersSet);
            }
            AppStateActions_Action::SetUsersTyping if action.has_socketIdList() => {
                let input_socket_ids = action.get_socketIdList().socketIDs.clone();
                new_room_state.users_typing = input_socket_ids.clone().into_iter().map(SocketId::from).collect();

                effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::UsersTypingSet, |effect| {
                    effect.set_socketIdList(action.get_socketIdList().clone());
                }));
            }
            AppStateActions_Action::UpdateClient if prev_state.deref().client_state.client.is_some() => {
                if let Some(client) = update_list_with_client(&mut new_room_state_users) {
                    if prev_state.client_state.is_client(&client) {
                        effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::UpdateUser, |effect| {
                            effect.set_clientSideUserDto(user_dto_to_client_side(client.get_userDto()));
                        }));

                        effects.push(pianorhythm_shared::util::create_effect_with(AppStateEffects_Action::ClientUpdated, |effect| {
                            effect.set_userClientDto(client.clone());
                        }));
                    }
                }
                new_room_state.users = new_room_state_users;
            }
            AppStateActions_Action::SetRoomChatMessages => {
                if action.has_roomChatHistory() {
                    new_room_state.chat_messages = action.get_roomChatHistory().lastMessages.clone().into_vec();

                    let mut effect = AppStateEffects::new();
                    effect.set_action(AppStateEffects_Action::SetRoomChatHistory);
                    let mut list = ChatMessageDtoList::new();
                    list.set_messages(RepeatedField::from_vec(new_room_state.chat_messages.clone()));
                    effect.set_chatMessageDtoList(list);
                    effects.push(effect);
                    events.push(AppStateEvents::RoomChatHistorySet);
                }
            }
            AppStateActions_Action::ClearChat => {
                new_room_state.chat_messages = vec![];
                effects.push(create_set_chat_messages_effect(&new_room_state.chat_messages));
            }
            AppStateActions_Action::ClearChatByUsername if action.has_usertag() => {
                let usertag = action.get_usertag().to_lowercase();
                new_room_state
                    .chat_messages
                    .retain_mut(|x| x.usertag.to_lowercase() != usertag || x.username.to_lowercase() != usertag);
                effects.push(create_set_chat_messages_effect(&new_room_state.chat_messages));
            }
            AppStateActions_Action::ClearChatBySocketID if action.has_socketId() => {
                new_room_state
                    .chat_messages
                    .retain_mut(|x| x.socketID.to_lowercase() != action.get_socketId().to_lowercase());
                effects.push(create_set_chat_messages_effect(&new_room_state.chat_messages));
            }
            AppStateActions_Action::AddRoomChatMessage if action.has_chatMessageDto() => {
                new_room_state.chat_messages.push(action.get_chatMessageDto().clone());

                let mut effect = AppStateEffects::new();
                effect.set_action(AppStateEffects_Action::AddChatMessage);
                effect.set_chatMessageDto(action.get_chatMessageDto().clone());
                effects.push(effect);
            }
            AppStateActions_Action::EditRoomChatMessage if action.has_chatMessageDto() => {
                let chat_message = action.get_chatMessageDto();
                if let Some(message) = new_room_state.chat_messages.iter_mut().find(|x| x.messageID == chat_message.messageID) {
                    message.set_message(chat_message.get_message().to_string());
                    let mut effect = AppStateEffects::new();
                    effect.set_action(AppStateEffects_Action::EditChatMessage);
                    effect.set_chatMessageDto(action.get_chatMessageDto().clone());
                    effects.push(effect);
                }
            }
            AppStateActions_Action::DeleteRoomChatMessage if action.has_stringValue() => {
                new_room_state.chat_messages.retain_mut(|x| x.messageID != action.get_stringValue());
                let mut effect = AppStateEffects::new();
                effect.set_action(AppStateEffects_Action::DeleteChatMessage);
                effect.set_messageId(action.get_stringValue().to_string());
                effects.push(effect);
            }
            AppStateActions_Action::SetRoomSettings if action.has_roomSettings() => {
                let details = action.get_roomSettings();
                new_room_state.room_settings = details.clone();

                if details.has_HostDetails() {
                    new_room_state.in_self_hosted_room = !details.get_HostDetails().get_CountryCode().is_empty();
                } else {
                    new_room_state.in_self_hosted_room = false;
                }

                let mut effect = AppStateEffects::new();
                effect.set_action(AppStateEffects_Action::SetRoomSettings);
                effect.set_roomSettings(details.clone());
                effects.push(effect);
            }
            AppStateActions_Action::SetCurrentRoomOwner if action.has_socketId() => {
                new_room_state.room_owner = Some(action.get_socketId().to_string());

                let mut effect = AppStateEffects::new();
                effect.set_action(AppStateEffects_Action::SetRoomOwner);
                effect.set_socketId(action.get_socketId().to_string());
                effects.push(effect);
            }
            AppStateActions_Action::SetUserChatMuted if action.has_sourceSocketID() => {
                let socket_id = action.get_sourceSocketID().to_string();
                if action.get_boolValue() {
                    new_room_state.muted_chat_users.insert(socket_id);
                } else {
                    new_room_state.muted_chat_users.remove(&socket_id);
                }
            }
            _ => {}
        }

        let mut new_state = prev_state.deref().clone();
        new_state.stop_emitting_to_ws_when_alone = new_room_state.users.len() <= 1;
        new_state.current_room_state = new_room_state.clone();

        if prev_state.stop_emitting_to_ws_when_alone != new_state.stop_emitting_to_ws_when_alone {
            let mut effect = AppStateEffects::new();
            effect.set_action(AppStateEffects_Action::EmittingNotesToServer);
            effect.set_boolValue(!new_state.stop_emitting_to_ws_when_alone);
            effects.push(effect);
        }

        if prev_state.current_room_state != new_state.current_room_state {
            events.push(AppStateEvents::RoomStateUpdated);
        }

        ReducerResult {
            state: Rc::new(new_state),
            events,
            effects,
        }
    }
}

macro_rules! can_update_with_command {
    ($source: expr, $command: expr, $field_name_has: ident, $field_name1: ident, $field_name2: ident) => {{
        || {
            if $command.$field_name_has() {
                let target_value = $command.$field_name1();
                if target_value != $source.$field_name2() {
                    return Some(target_value);
                }
            }
            return None;
        }
    }};
}

pub fn update_user_from_command(user_dto: &UserDto, user_update_command: &UserUpdateCommand) -> UserDto {
    let mut user_dto = user_dto.clone();
    let mut user_update_command = user_update_command.clone();

    if user_dto.get_socketID() == user_update_command.get_socketID() {
        // meta
        if user_update_command.has_clientMetaDetails() {
            let mut user_meta = user_dto.meta.clone().unwrap_or_default();
            user_meta.set_clientMetaDetails(user_update_command.take_clientMetaDetails());
            user_dto.set_meta(user_meta);
        }

        // color
        if let Some(target) = can_update_with_command!(user_dto, user_update_command, has_userColor, get_userColor, get_color)() {
            user_dto.set_color(target.to_string());
        }

        // status
        if let Some(target) = can_update_with_command!(user_dto, user_update_command, has_userStatus, get_userStatus, get_status)() {
            user_dto.set_status(target);
        }

        // status text
        if let Some(target) = can_update_with_command!(user_dto, user_update_command, has_statusText, take_statusText, get_statusText)() {
            user_dto.set_statusText(target);
        }

        // nickname
        if let Some(target) = can_update_with_command!(user_dto, user_update_command, has_nickname, take_nickname, get_nickname)() {
            user_dto.set_nickname(target);
        }

        // profile description
        if let Some(target) =
            can_update_with_command!(user_dto, user_update_command, has_profileDescription, take_profileDescription, get_ProfileDescription)()
        {
            user_dto.set_ProfileDescription(target);
        }

        // self muted
        if user_update_command.has_selfMuted() {
            user_dto.set_selfMuted(user_update_command.get_selfMuted());
        }

        // server chat muted
        if user_update_command.has_serverChatMuted() {
            user_dto.set_serverChatMuted(user_update_command.get_serverChatMuted());
        }

        // server notes muted
        if user_update_command.has_serverNotesMuted() {
            user_dto.set_serverNotesMuted(user_update_command.get_serverNotesMuted());
        }

        // profile image
        if let Some(target) =
            can_update_with_command!(user_dto, user_update_command, has_profileImageUpdated, take_profileImageUpdated, get_profileImageLastModified)()
        {
            user_dto.set_profileImageLastModified(target);
        }

        // profile bg image
        if let Some(target) = can_update_with_command!(
            user_dto,
            user_update_command,
            has_profileBackgroundImageUpdated,
            take_profileBackgroundImageUpdated,
            get_profileBackgroundImageLastModified
        )() {
            user_dto.set_profileBackgroundImageLastModified(target);
        }

        // Profile images cleared
        if user_update_command.get_profileImageCleared() {
            user_dto.clear_profileImageLastModified();
        }

        if user_update_command.get_profileBackgroundImageCleared() {
            user_dto.clear_profileBackgroundImageLastModified();
        }

        // Orchestra Model
        if user_update_command.has_orchestraModel() {
            let mut world_data = user_dto.worldData.clone().unwrap_or_default();
            world_data.set_orchestraModelCustomizationDataJSON(user_update_command.get_orchestraModel().to_string());
            user_dto.set_worldData(world_data);
        }

        // avatar
        if user_update_command.has_avatarWorldPosition() {
            let mut world_data = user_dto.worldData.clone().unwrap_or_default();
            let mut avatar_world_data = world_data.get_avatarWorldData().clone();
            avatar_world_data.set_worldPosition(user_update_command.get_avatarWorldPosition().clone());
            world_data.set_avatarWorldData(avatar_world_data);
            user_dto.set_worldData(world_data);
        }

        if user_update_command.has_avatarPianoBenchSeat() {
            let mut world_data = user_dto.worldData.clone().unwrap_or_default();
            let mut avatar_world_data = world_data.get_avatarWorldData().clone();
            avatar_world_data.set_pianoBenchSeat(user_update_command.get_avatarPianoBenchSeat().clone());
            world_data.set_avatarWorldData(avatar_world_data);
            user_dto.set_worldData(world_data);
        }

        // badges
        if user_update_command.badges.len() > 0 && user_update_command.badges != user_dto.badges {
            user_dto.set_badges(user_update_command.badges);
        }
    }

    user_dto
}

#[cfg(test)]
mod tests {
    use pianorhythm_proto::user_renditions::{UserClientDto, UserStatus};

    use super::*;

    #[test]
    fn given_update_user_from_command_when_command_has_color_then_user_color_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_userColor("red".to_string());

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_color(), "red");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_status_then_user_status_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_userStatus(UserStatus::Online);

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_status(), UserStatus::Online);
    }

    #[test]
    fn given_update_user_from_command_when_command_has_status_text_then_user_status_text_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_statusText("hello".to_string());

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_statusText(), "hello");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_nickname_then_user_nickname_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_nickname("hello".to_string());

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_nickname(), "hello");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_profile_description_then_user_profile_description_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_profileDescription("hello".to_string());

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_ProfileDescription(), "hello");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_self_muted_then_user_self_muted_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_selfMuted(true);

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_selfMuted(), true);
    }

    #[test]
    fn given_update_user_from_command_when_command_has_server_chat_muted_then_user_server_chat_muted_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_serverChatMuted(true);

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_serverChatMuted(), true);
    }

    #[test]
    fn given_update_user_from_command_when_command_has_server_notes_muted_then_user_server_notes_muted_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_serverNotesMuted(true);

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_serverNotesMuted(), true);
    }

    #[test]
    fn given_update_user_from_command_when_command_has_profile_image_updated_then_user_profile_image_last_modified_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_profileImageUpdated("01-01-0000".to_string());

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_profileImageLastModified(), "01-01-0000");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_profile_background_image_updated_then_user_profile_background_image_last_modified_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_profileBackgroundImageUpdated("01-01-0000".to_string());

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_profileBackgroundImageLastModified(), "01-01-0000");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_profile_image_cleared_then_user_profile_image_last_modified_is_cleared() {
        let mut user_dto = UserDto::new();
        user_dto.set_profileImageLastModified("01-01-0000".to_string());
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_profileImageCleared(true);

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_profileImageLastModified(), "");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_profile_background_image_cleared_then_user_profile_background_image_last_modified_is_cleared() {
        let mut user_dto = UserDto::new();
        user_dto.set_profileBackgroundImageLastModified("01-01-0000".to_string());
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_profileBackgroundImageCleared(true);

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_profileBackgroundImageLastModified(), "");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_orchestra_model_then_user_orchestra_model_customization_data_json_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_orchestraModel("{'key':'value'}".to_string());

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.worldData.unwrap().get_orchestraModelCustomizationDataJSON(), "{'key':'value'}");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_badges_then_user_badges_are_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();

        let mut badge = pianorhythm_proto::user_renditions::UserBadges::new();
        badge.set_badge(pianorhythm_proto::user_renditions::Badges::PRO_MEMBER);
        user_update_command.set_badges(RepeatedField::from_vec(vec![badge.clone()]));

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.get_badges(), vec![badge.clone()]);
    }

    #[test]
    fn given_update_user_from_command_when_command_has_client_meta_details_then_user_meta_client_meta_details_is_updated() {
        let user_dto = UserDto::new();
        let mut user_update_command = UserUpdateCommand::new();
        user_update_command.set_clientMetaDetails("{'key':'value'}".to_string());

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result.meta.unwrap().get_clientMetaDetails(), "{'key':'value'}");
    }

    #[test]
    fn given_update_user_from_command_when_command_has_nothing_then_user_dto_is_unchanged() {
        let user_dto = UserDto::new();
        let user_update_command = UserUpdateCommand::new();

        let result = update_user_from_command(&user_dto, &user_update_command);

        assert_eq!(result, user_dto);
    }

    #[test]
    fn given_joined_room_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut room_settings = RoomSettings::new();
        let mut room_stage_details = RoomStageDetails::new();
        let mut joined_room_data = pianorhythm_proto::client_message::JoinedRoomData::new();
        joined_room_data.set_roomID("room_id".to_string());
        room_stage_details.set_stage(RoomStages::THE_VOID);
        room_settings.set_StageDetails(room_stage_details.clone());
        joined_room_data.set_roomSettings(room_settings.clone());
        action.set_joinedRoomData(joined_room_data.clone());
        action.set_action(AppStateActions_Action::JoinedRoom);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.room_id, Some(RoomId::from("room_id")));
        assert_eq!(result.state.current_room_state.room_stage_details, Some(room_stage_details));
        assert_eq!(result.state.current_room_state.room_settings, room_settings);
        assert_eq!(result.state.current_room_state.room_stage_loading, true);
        assert_eq!(result.events.contains(&AppStateEvents::RoomStageLoading), true);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::JoinedRoomSuccess), true);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::LoadRoomStage), true);
    }

    #[test]
    fn given_set_room_stage_loaded_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        action.set_boolValue(true);
        action.set_action(AppStateActions_Action::SetRoomStageLoaded);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.room_stage_loaded, true);
        assert_eq!(result.state.current_room_state.room_stage_loading, false);
        assert_eq!(result.events.contains(&AppStateEvents::RoomStageLoaded), true);
    }

    #[test]
    fn given_add_user_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut user_dto = UserDto::new();
        user_dto.set_socketID("socket_id".to_string());
        action.set_userDto(user_dto.clone());
        action.set_action(AppStateActions_Action::AddUser);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.users.get(&SocketId::from("socket_id")).is_some(), true);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::AddUser), true);
    }

    #[test]
    fn given_update_user_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut user_dto = UserDto::new();
        user_dto.set_socketID("socket_id".to_string());
        action.set_userUpdateCommand(UserUpdateCommand::new());
        action.set_action(AppStateActions_Action::UpdateUser);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.effects.iter().any(|x| x.action != AppStateEffects_Action::UpdateUser), true);
    }

    #[test]
    fn given_remove_user_action_when_reducing_should_update_state() {
        let mut prev_state = Rc::new(AppState::default());
        let reducer = CurrentRoomStateReducer;

        // Add user first
        let mut add_user_action = AppStateActions::new();
        let mut user_dto = UserDto::new();
        user_dto.set_socketID("socket_id".to_string());
        add_user_action.set_userDto(user_dto.clone());
        add_user_action.set_action(AppStateActions_Action::AddUser);
        prev_state = reducer.reduce(&prev_state, &add_user_action).state;

        let mut action = AppStateActions::new();
        action.set_socketId("socket_id".to_string());
        action.set_action(AppStateActions_Action::RemoveUser);

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.users.get(&SocketId::from("socket_id")).is_none(), true);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::RemoveUser), true);
    }

    #[test]
    fn given_set_users_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut user_dto = UserDto::new();
        user_dto.set_socketID("socket_id".to_string());

        let mut list = pianorhythm_proto::client_message::UserDtoList::new();
        list.userDto.push(user_dto.clone());
        action.set_userDtoList(list);
        action.set_action(AppStateActions_Action::SetUsers);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.users.get(&SocketId::from("socket_id")).is_some(), true);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::UsersSet), true);
    }

    #[test]
    fn given_set_users_typing_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut list = pianorhythm_proto::client_message::SocketIdList::new();
        list.socketIDs.push("socket_id".to_string());
        action.set_socketIdList(list);
        action.set_action(AppStateActions_Action::SetUsersTyping);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.users_typing.contains(&SocketId::from("socket_id")), true);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::UsersTypingSet), true);
    }

    #[test]
    #[ignore]
    fn given_update_client_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut user_dto = UserDto::new();
        user_dto.set_socketID("socket_id".to_string());

        let mut list = pianorhythm_proto::client_message::UserDtoList::new();
        list.userDto.push(user_dto.clone());
        action.set_userDtoList(list);
        action.set_action(AppStateActions_Action::SetUsers);

        let reducer = CurrentRoomStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut client = UserClientDto::new();

        // set client first by action through the reducer
        client.set_userDto(user_dto.clone());

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.users.get(&SocketId::from("socket_id")).is_some(), true);
        assert_eq!(result.effects.len(), 2);
    }

    #[test]
    fn given_set_room_chat_messages_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut room_chat_history = pianorhythm_proto::client_message::RoomChatHistory::new();
        let mut chat_message_dto = pianorhythm_proto::client_message::ChatMessageDto::new();
        chat_message_dto.set_messageID("message_id".to_string());
        room_chat_history.lastMessages.push(chat_message_dto.clone());
        action.set_roomChatHistory(room_chat_history);
        action.set_action(AppStateActions_Action::SetRoomChatMessages);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.chat_messages.len(), 1);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::SetRoomChatHistory), true);
    }

    #[test]
    #[ignore]
    fn given_clear_chat_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        action.set_action(AppStateActions_Action::ClearChat);

        let reducer = CurrentRoomStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut chat_message_dto = pianorhythm_proto::client_message::ChatMessageDto::new();
        chat_message_dto.set_messageID("message_id".to_string());
        // prev_state.current_room_state.chat_messages.push(chat_message_dto.clone());

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.chat_messages.len(), 0);
        assert_eq!(result.effects.len(), 1);
    }

    #[test]
    #[ignore]
    fn given_clear_chat_by_username_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        action.set_usertag("username".to_string());
        action.set_action(AppStateActions_Action::ClearChatByUsername);

        let reducer = CurrentRoomStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut chat_message_dto = pianorhythm_proto::client_message::ChatMessageDto::new();
        chat_message_dto.set_messageID("message_id".to_string());
        chat_message_dto.set_username("username".to_string());
        // prev_state.current_room_state.chat_messages.push(chat_message_dto.clone());

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.chat_messages.len(), 0);
        assert_eq!(result.effects.len(), 1);
    }

    #[test]
    #[ignore]
    fn given_clear_chat_by_socket_id_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        action.set_socketId("socket_id".to_string());
        action.set_action(AppStateActions_Action::ClearChatBySocketID);

        let reducer = CurrentRoomStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut chat_message_dto = pianorhythm_proto::client_message::ChatMessageDto::new();
        chat_message_dto.set_messageID("message_id".to_string());
        chat_message_dto.set_socketID("socket_id".to_string());
        // prev_state.current_room_state.chat_messages.push(chat_message_dto.clone());

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.chat_messages.len(), 0);
        assert_eq!(result.effects.len(), 1);
    }

    #[test]
    fn given_add_room_chat_message_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut chat_message_dto = pianorhythm_proto::client_message::ChatMessageDto::new();
        chat_message_dto.set_messageID("message_id".to_string());
        action.set_chatMessageDto(chat_message_dto.clone());
        action.set_action(AppStateActions_Action::AddRoomChatMessage);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.chat_messages.len(), 1);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::AddChatMessage), true);
    }

    #[test]
    #[ignore]
    fn given_edit_room_chat_message_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut chat_message_dto = pianorhythm_proto::client_message::ChatMessageDto::new();
        chat_message_dto.set_messageID("message_id".to_string());
        action.set_chatMessageDto(chat_message_dto.clone());
        action.set_action(AppStateActions_Action::EditRoomChatMessage);

        let reducer = CurrentRoomStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut chat_message_dto = pianorhythm_proto::client_message::ChatMessageDto::new();
        chat_message_dto.set_messageID("message_id".to_string());
        // prev_state.current_room_state.chat_messages.push(chat_message_dto.clone());

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.chat_messages.len(), 0);
        assert_eq!(result.effects.len(), 1);
    }

    #[test]
    fn given_delete_room_chat_message_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        action.set_stringValue("message_id".to_string());
        action.set_action(AppStateActions_Action::DeleteRoomChatMessage);

        let reducer = CurrentRoomStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut chat_message_dto = pianorhythm_proto::client_message::ChatMessageDto::new();
        chat_message_dto.set_messageID("message_id".to_string());
        // prev_state.current_room_state.chat_messages.push(chat_message_dto.clone());

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.chat_messages.len(), 0);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::DeleteChatMessage), true);
    }

    #[test]
    fn given_set_room_settings_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        let mut room_settings = RoomSettings::new();
        room_settings.set_RoomType(pianorhythm_proto::room_renditions::RoomType::Lobby);
        action.set_roomSettings(room_settings.clone());
        action.set_action(AppStateActions_Action::SetRoomSettings);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.room_settings, room_settings);
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::SetRoomSettings), true);
    }

    #[test]
    fn given_set_current_room_owner_action_when_reducing_should_update_state() {
        let mut action = AppStateActions::new();
        action.set_socketId("socket_id".to_string());
        action.set_action(AppStateActions_Action::SetCurrentRoomOwner);

        let reducer = CurrentRoomStateReducer;
        let prev_state = Rc::new(AppState::default());
        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.room_owner, Some("socket_id".to_string()));
        assert_eq!(result.effects.iter().any(|x| x.action == AppStateEffects_Action::SetRoomOwner), true);
    }

    #[test]
    #[ignore]
    fn given_update_client_action_when_reducing_should_update_state_and_effects() {
        let mut action = AppStateActions::new();
        let mut user_dto = UserDto::new();
        user_dto.set_socketID("socket_id".to_string());
        action.set_userDto(user_dto.clone());
        action.set_action(AppStateActions_Action::UpdateClient);

        let reducer = CurrentRoomStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut client = UserClientDto::new();
        client.set_userDto(user_dto.clone());
        // prev_state.client_state.client = Some(client.clone());

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.stop_emitting_to_ws_when_alone, true);
        assert_eq!(result.effects.len(), 2);
    }

    #[test]
    #[ignore]
    fn given_clear_chat_by_username_action_when_reducing_should_update_state_and_effects() {
        let mut action = AppStateActions::new();
        action.set_usertag("username".to_string());
        action.set_action(AppStateActions_Action::ClearChatByUsername);

        let reducer = CurrentRoomStateReducer;
        let mut prev_state = Rc::new(AppState::default());
        let mut chat_message_dto = pianorhythm_proto::client_message::ChatMessageDto::new();
        chat_message_dto.set_messageID("message_id".to_string());
        chat_message_dto.set_username("username".to_string());
        // prev_state.current_room_state.chat_messages.push(chat_message_dto.clone());

        let result = reducer.reduce(&prev_state, &action);

        assert_eq!(result.state.current_room_state.chat_messages.len(), 0);
        assert_eq!(result.effects.len(), 1);
    }
}